<template>
	<view class="question-index">
		<view class="nav-bar"></view>
		<!-- 专业切换 -->
		<view class="header-box">
			<view class="header">
				<view v-if="false" class="head" @click="goDetail('pages/userInfo/index')">
					<image
						src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16693568262487366166935682624823545_touxiang.png"
						mode="widthFix" />
				</view>
				<view class="major flex-center" @click="selectMajorFn">
					<text>{{ major_name }}</text>
					<image src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/down.png" mode="widthFix" />
				</view>
			</view>
		</view>
		<view class="content">
			<template>
				<view>
					<view class="title" style="margin-bottom: 20rpx">
						<img style="margin-right: 10rpx; width: 25rpx;height: 25rpx;"
							src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/title-icon.png" alt="" />
						<text>秒杀</text>
					</view>

					<swiper class="seckill" autoplay>
						<swiper-item v-for="(item, index) in recommendList" :key="item.id || index">
							<view class="swiper-item">
								<examinationTestItem :seckill="true" :item="item"></examinationTestItem>
							</view>
						</swiper-item>
					</swiper>
				</view>
				<view class="tabs">
					<view class="tab-item" v-for="item of tabs" :class="{
            active: item.id == tabIndex
          }" @click="tabIndex = item.id">
						<view class="name">{{ item.name }}</view>
						<view v-if="item.id == tabIndex" class="bottom-line">

						</view>
					</view>
				</view>
				<view v-if="tabIndex == 1">
					<examination-test-list :data="goodsList810" :isPay="false"></examination-test-list>
				</view>

				<view v-if="tabIndex == 2">
					<examination-test-list :data="goodsList18" :isPay="false"></examination-test-list>
				</view>
				<view class="not_data" v-if="false">
					<view class="img">
						<img src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
							alt="" />
					</view>
					<view class="desc">登录后查看相关测验哦！</view>
					<view class="button goLogin" @click="goDetail('pages/loginCenter/index')">
						去登录
					</view>
				</view>
			</template>
		</view>
		<!-- 选择专业组件 -->
		<select-major v-model="majorid" :show.sync="majorshow" :major_name.sync="major_name"
			@input="chackMajor"></select-major>
	</view>
</template>
<script>
	import selectMajor from '../../components/select-major.vue'
	import noData from '../../components/commen/no-data.vue'
	import {
		getAllExam,
		getPreInfo,
		getGoods
	} from '../../api/index'
	import examinationTestList from '../../components/makeQuestion/examination-test-list.vue'
	import examinationTestItem from '../../components/makeQuestion/examination-test-item.vue'
	import {
		goToLogin,
		goToMajor
	} from '../../utils/index'
	import {
		shelf_platform_id
	} from '../../config'
	export default {
		components: {
			selectMajor,
			noData,
			examinationTestList,
			examinationTestItem
		},
		data() {
			return {
				tabIndex: '1',
				tabs: [{
						name: '测验',
						id: '1'
					},
					{
						name: '练习',
						id: '2'
					}
				],
				majorid: '',
				major_name: '选择专业',
				majorshow: false,
				index: 0,
				testLists: [],
				preInfo: {
					scene: 0,
					practice_progress_text: ''
				},
				// 上次练习的信息
				urlInfo: {
					1: 'pages/chapterExercise/index', // 章节练习
					2: 'pages/questionChallenge/index', // 真题闯关
					3: 'pages/intelligentEvaluation/index', // 智能测评
					5: 'pages/examEntry/index', // 考点词条
					6: 'pages/modelExaminationCompetition/index'
				},
				goodsList18: [],
				goodsList810: [],
				recommendList: [],
			}
		},
		onLoad(e) {
			this.init(e)
		},
		onShow() {
			this.startFun()
		},
		onHide() {
			this.majorshow = false
		},
		methods: {
			updata() {
				this.goodsList18 = []
				this.goodsList810 = []
				this.recommendList = []
				this.getGoods()
			},
			getGoods() {
				// if (!this.isLogin()) {
				//   uni.stopPullDownRefresh()
				//   return
				// }
				if (this.goodsList18.length > 0 || this.goodsList810.length > 0) {
					return
				}
				let {
					major_id = '', major_name = ''
				} =
				uni.getStorageSync('__xingyun_major__')
				getGoods({
					shelf_platform_id,
					professional_id: major_id,
					type: 18
				}).then(res => {
					this.goodsList18 = res.data.list
					
					this.recommendList = res.data.list;
					
					this.recommendList = this.recommendList.concat(
						res.data.list.filter(
							e => e.is_homepage_recommend == 1 && e.permission_status == '2'
						)
					)
				})
				getGoods({
					shelf_platform_id,
					professional_id: major_id,
					type: '8,10'
				}).then(res => {
					this.goodsList810 = res.data.list
					this.recommendList = this.recommendList.concat(
						res.data.list.filter(
							e => e.is_homepage_recommend == 1 && e.permission_status == '2'
						)
					)
					uni.stopPullDownRefresh()
				})
			},
			init(e) {
				let {
					employee_id = '',
						promoter_id = '',
						promoter_type = '',
						student_id = ''
				} = e
				if (employee_id) {
					uni.setStorageSync('__xingyun_share__', {
						employee_id,
						promoter_id,
						promoter_type,
						student_id
					})
				}
			},
			isLogin() {
				return !!this.$store.state.jintiku.token
			},
			getTestExm() {
				if (!this.isLogin()) {
					return
				}
				let data = {
					exam_type: '1,2,3',
					from_client: 'ios',
					source: '2',
					noloading: true
				}
				getAllExam(data).then(res => {
					this.testLists = res.data.list ? res.data.list : []
				})
			},
			goDetail(url) {
				if (!this.isLogin()) {
					// this.$xh.push('jintiku', 'pages/loginCenter/index')
					goToLogin('goToLogin22')
					return
				}
				this.$xh.push('jintiku', url)
			},
			startFun() {
				let {
					major_id = '', major_name = ''
				} =
				uni.getStorageSync('__xingyun_major__')
				console.log(major_id, '这专业ID是啥？？')
				if (this.majorid && major_id && this.majorid != major_id) {
					this.major_name = major_name
					this.majorid = major_id
					this.updata()
				} else if (major_id && major_name) {
					this.major_name = major_name
					this.majorid = major_id
					this.getGoods()
				} else {
					if (!this.isLogin()) {
						return
					}
					// this.$xh.push('jintiku', `pages/major/index`)
					goToMajor()
					return
				}
				// this.getInfo()
				// this.getPreDesc()
			},
			getInfo() {
				this.getTestExm()
			},
			getPreDesc() {
				if (!this.isLogin()) {
					return
				}
				getPreInfo({
					noloading: true
				}).then(data => {
					this.preInfo = data.data
				})
			},
			chackMajor(value) {
				this.major_id = value

				setTimeout(() => {
					this.updata()
					this.getInfo()
				}, 500)
			},
			preDetail() {
				if (urlInfo[this.preInfo.scene]) {
					this.$xh.push('jintiku', urlInfo[this.preInfo.scene])
				}
			},
			selectMajorFn() {
				if (!this.isLogin()) {
					this.goDetail('pages/loginCenter/index')
					return
				}
				this.majorshow = !this.majorshow
			}
		},
		onPullDownRefresh() {
			this.updata()
		},
		onShareAppMessage() {
			return this.$xh.shareAppMessage()
		}
	}
</script>

<style scoped lang="less">
	.question-index {
		background-color: #fff;
		min-height: 100vh;
		overflow-y: auto;
		font-family: PingFangSC-Regular, PingFang SC;

		// .nav-bar {
		//   height: --status-bar-height;
		//   background: red;
		//   width: 100%;
		// }
		.header-box {
			padding-top: 80rpx;
			/* #ifdef H5 */
			padding-top: 0rpx;
			/* #endif */
			height: 100rpx;
			background-color: white;
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			z-index: 3;


			.header {
				position: relative;
				height: calc(96rpx);
				background-color: white;
				display: flex;
				align-items: center;
				// justify-content: center;
				padding-left: 24rpx;

				// margin-top: --status-bar-height;
				.head {
					position: absolute;
					height: 64rpx;
					width: 64rpx;
					overflow: hidden;
					border-radius: 50%;
					left: 30rpx;
					top: 0;
					bottom: 0;
					margin: auto 0;

					image {
						width: 100%;
						height: auto;
					}
				}

				.major {
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 40rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: black;
						line-height: 40rpx;
					}

					image {
						width: 20rpx;
						height: 20rpx;
						margin-left: 20rpx;
					}

					&.active {
						text {}
					}
				}
			}
		}



		.content {
			background: #fff;
			//height: calc(100vh - 500rpx);
			position: relative;
			z-index: 1;
			margin-top: 175rpx;
			/* #ifdef H5 */
			margin-top: 90rpx;
			/* #endif */
			border-top-right-radius: 20rpx;
			border-top-left-radius: 20rpx;
			padding: 32rpx 24rpx 60rpx 24rpx;

			.seckill {
				width: 100vw;
				position: relative;
				left: -24rpx;
				height: 270rpx;

				.swiper-item {
					padding: 24rpx 24rpx 0rpx 24rpx;
				}
			}

			.title {
				font-size: 32rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 600;
				color: #161f30;
				line-height: 32rpx;
				margin-bottom: 24rpx;
				display: flex;
				align-items: center;

				.line {
					width: 6rpx;
					height: 30rpx;
					background: #2e68ff;
					border-radius: 4rpx;
					margin-right: 10rpx;
				}
			}

			.cards {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 52rpx;

				.card {
					width: 340rpx;
					height: 90rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #eff4ff;
					border-radius: 16rpx;
					padding: 0 24rpx;

					text {
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #000;
						line-height: 30rpx;
						margin-right: 46rpx;
					}

					image {
						width: 12rpx;
						height: 22rpx;
					}

					.left {
						image {
							width: 34rpx;
							height: 34rpx;
							margin-right: 20rpx;
						}
					}
				}
			}

			.test-card {
				margin-bottom: 40rpx;

				.conteiner {
					border-radius: 16rpx;
					padding: 24rpx 32rpx;
					box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
				}

				.test-card-title {
					font-size: 30rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #212121;
					line-height: 42rpx;
					margin-bottom: 16rpx;
				}

				.date-time {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #898a8d;
					line-height: 24rpx;
					margin-bottom: 32rpx;
				}

				.line {
					height: 1rpx;
					background-color: #e8e9ea;
					margin-bottom: 36rpx;
				}

				.bottom {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.left-name {
						display: flex;
						align-items: center;

						.name-title,
						.desc,
						.time {
							font-size: 22rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #8a8b8c;
							line-height: 32rpx;
						}

						.name-title {
							font-weight: 800;
							color: #000;
						}

						.time {
							margin-left: 32rpx;
						}
					}
				}

				.button {
					width: 160rpx;
					height: 56rpx;
					line-height: 56rpx;
					background: #2e68ff;
					border-radius: 64px;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					text-align: center;
				}
			}
		}

		.last-time {
			position: fixed;
			left: 0;
			bottom: 0;
			height: 74rpx;
			background-color: #eff5ff;
			z-index: 2;
			padding: 0 24rpx;
			width: 100%;
			display: flex;
			justify-content: space-between;

			.title {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #2e68ff;

				.tow {
					font-weight: 800;
				}
			}

			.continue {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #2e68ff;
					margin-right: 16rpx;
				}

				image {
					width: 26rpx;
					height: 26rpx;
				}
			}
		}

		.not_data {
			width: 100%;
			padding-top: 120rpx;

			.img {
				width: 229rpx;
				height: 180rpx;
				margin: 0 auto;

				img {
					width: 100%;
					height: 100%;
				}
			}

			.desc {
				text-align: center;
				font-size: 26rpx;
				margin-top: 40rpx;
				color: #ccc;
			}

			.goLogin {
				width: 260rpx;
				height: 80rpx;
				background: #2e68ff;
				color: #ffffff;
				font-size: 28rpx;
				text-align: center;
				line-height: 80rpx;
				border-radius: 38rpx;
				margin: 40rpx auto;
			}
		}
	}

	.tabs {
		height: 70rpx;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.tab-item {
			margin-right: 40rpx;
			width: 112rpx;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			position: relative;

			.name {
				font-weight: 400;
				font-size: 32rpx;
				color: #666666;
				text-align: center;
			}

			.bottom-line {
				width: 80%;
				height: 8rpx;
				background-color: #018BFF;
			}

			.img {
				display: none;
			}
		}

		.active {
			.name {
				font-weight: 500;
				font-size: 36rpx;
				color: #000000;
			}

			.img {
				display: block;
				position: absolute;
				bottom: 0;
				left: calc(50% - 18rpx);
				width: 36rpx;
				height: 16rpx;
			}
		}
	}
</style>